import { 
  adminFunctions, 
  createSessionServices,
  AppwriteServerError,
  logger,
  ID
} from '@/lib/appwrite-server';
import { BaseAppwriteService, ServiceResult, PaginationParams, PaginatedResult } from './base';

// Functions interfaces
export interface FunctionInfo {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  execute: string[];
  name: string;
  enabled: boolean;
  live: boolean;
  logging: boolean;
  runtime: string;
  deployment?: string;
  vars: Record<string, string>;
  events: string[];
  schedule: string;
  timeout: number;
  entrypoint: string;
  commands: string;
  version: string;
  installationId: string;
  providerRepositoryId: string;
  providerBranch: string;
  providerRootDirectory: string;
  providerSilentMode: boolean;
}

export interface DeploymentInfo {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  type: string;
  resourceId: string;
  resourceType: string;
  entrypoint: string;
  size: number;
  buildId: string;
  activate: boolean;
  status: string;
  buildStdout: string;
  buildStderr: string;
  buildTime: number;
}

export interface ExecutionInfo {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  functionId: string;
  trigger: string;
  status: string;
  requestMethod: string;
  requestPath: string;
  requestHeaders: Record<string, string>;
  responseStatusCode: number;
  responseHeaders: Record<string, string>;
  logs: string;
  errors: string;
  duration: number;
}

export interface CreateFunctionParams {
  name: string;
  functionId?: string;
  runtime: string;
  execute?: string[];
  events?: string[];
  schedule?: string;
  timeout?: number;
  enabled?: boolean;
  logging?: boolean;
  entrypoint?: string;
  commands?: string;
  installationId?: string;
  providerRepositoryId?: string;
  providerBranch?: string;
  providerRootDirectory?: string;
  providerSilentMode?: boolean;
}

export interface UpdateFunctionParams {
  functionId: string;
  name?: string;
  runtime?: string;
  execute?: string[];
  events?: string[];
  schedule?: string;
  timeout?: number;
  enabled?: boolean;
  logging?: boolean;
  entrypoint?: string;
  commands?: string;
  installationId?: string;
  providerRepositoryId?: string;
  providerBranch?: string;
  providerRootDirectory?: string;
  providerSilentMode?: boolean;
}

export interface CreateDeploymentParams {
  functionId: string;
  code: File | Buffer;
  activate?: boolean;
  entrypoint?: string;
  commands?: string;
}

export interface ExecuteFunctionParams {
  functionId: string;
  body?: string;
  async?: boolean;
  xpath?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  headers?: Record<string, string>;
}

export interface FunctionVariable {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  key: string;
  value: string;
  functionId: string;
}

// Functions service class
export class FunctionsService extends BaseAppwriteService {
  constructor() {
    super('FunctionsService');
  }

  // Create function
  async createFunction(params: CreateFunctionParams): Promise<ServiceResult<FunctionInfo>> {
    return this.executeOperation('createFunction', async () => {
      this.validateRequired(params, ['name', 'runtime']);

      const func = await adminFunctions.create(
        params.functionId || ID.unique(),
        params.name,
        params.runtime,
        params.execute,
        params.events,
        params.schedule,
        params.timeout,
        params.enabled,
        params.logging,
        params.entrypoint,
        params.commands,
        params.installationId,
        params.providerRepositoryId,
        params.providerBranch,
        params.providerRootDirectory,
        params.providerSilentMode
      );

      logger.info(`Function created: ${func.$id}`);
      return func as FunctionInfo;
    });
  }

  // Get function
  async getFunction(functionId: string): Promise<ServiceResult<FunctionInfo>> {
    return this.executeOperation('getFunction', async () => {
      this.validateRequired({ functionId }, ['functionId']);

      const func = await adminFunctions.get(functionId);
      return func as FunctionInfo;
    });
  }

  // List functions
  async listFunctions(pagination?: PaginationParams): Promise<ServiceResult<PaginatedResult<FunctionInfo>>> {
    return this.executeOperation('listFunctions', async () => {
      const validatedPagination = this.validatePagination(pagination);

      const result = await adminFunctions.list(
        undefined, // queries
        validatedPagination.limit,
        validatedPagination.offset
      );

      const paginatedResult = this.formatPaginatedResult(
        result.functions as FunctionInfo[],
        result.total,
        validatedPagination
      );

      return paginatedResult;
    });
  }

  // Update function
  async updateFunction(params: UpdateFunctionParams): Promise<ServiceResult<FunctionInfo>> {
    return this.executeOperation('updateFunction', async () => {
      this.validateRequired(params, ['functionId']);

      const func = await adminFunctions.update(
        params.functionId,
        params.name,
        params.runtime,
        params.execute,
        params.events,
        params.schedule,
        params.timeout,
        params.enabled,
        params.logging,
        params.entrypoint,
        params.commands,
        params.installationId,
        params.providerRepositoryId,
        params.providerBranch,
        params.providerRootDirectory,
        params.providerSilentMode
      );

      logger.info(`Function updated: ${params.functionId}`);
      return func as FunctionInfo;
    });
  }

  // Delete function
  async deleteFunction(functionId: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('deleteFunction', async () => {
      this.validateRequired({ functionId }, ['functionId']);

      await adminFunctions.delete(functionId);

      logger.info(`Function deleted: ${functionId}`);
      return true;
    });
  }

  // Create deployment
  async createDeployment(params: CreateDeploymentParams): Promise<ServiceResult<DeploymentInfo>> {
    return this.executeOperation('createDeployment', async () => {
      this.validateRequired(params, ['functionId', 'code']);

      const deployment = await adminFunctions.createDeployment(
        params.functionId,
        params.code,
        params.activate,
        params.entrypoint,
        params.commands
      );

      logger.info(`Deployment created: ${deployment.$id} for function: ${params.functionId}`);
      return deployment as DeploymentInfo;
    });
  }

  // Get deployment
  async getDeployment(functionId: string, deploymentId: string): Promise<ServiceResult<DeploymentInfo>> {
    return this.executeOperation('getDeployment', async () => {
      this.validateRequired({ functionId, deploymentId }, ['functionId', 'deploymentId']);

      const deployment = await adminFunctions.getDeployment(functionId, deploymentId);
      return deployment as DeploymentInfo;
    });
  }

  // List deployments
  async listDeployments(
    functionId: string,
    pagination?: PaginationParams
  ): Promise<ServiceResult<PaginatedResult<DeploymentInfo>>> {
    return this.executeOperation('listDeployments', async () => {
      this.validateRequired({ functionId }, ['functionId']);

      const validatedPagination = this.validatePagination(pagination);

      const result = await adminFunctions.listDeployments(
        functionId,
        undefined, // queries
        validatedPagination.limit,
        validatedPagination.offset
      );

      const paginatedResult = this.formatPaginatedResult(
        result.deployments as DeploymentInfo[],
        result.total,
        validatedPagination
      );

      return paginatedResult;
    });
  }

  // Update deployment
  async updateDeployment(functionId: string, deploymentId: string): Promise<ServiceResult<FunctionInfo>> {
    return this.executeOperation('updateDeployment', async () => {
      this.validateRequired({ functionId, deploymentId }, ['functionId', 'deploymentId']);

      const func = await adminFunctions.updateDeployment(functionId, deploymentId);

      logger.info(`Deployment updated: ${deploymentId} for function: ${functionId}`);
      return func as FunctionInfo;
    });
  }

  // Delete deployment
  async deleteDeployment(functionId: string, deploymentId: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('deleteDeployment', async () => {
      this.validateRequired({ functionId, deploymentId }, ['functionId', 'deploymentId']);

      await adminFunctions.deleteDeployment(functionId, deploymentId);

      logger.info(`Deployment deleted: ${deploymentId} from function: ${functionId}`);
      return true;
    });
  }

  // Execute function
  async executeFunction(params: ExecuteFunctionParams, sessionId?: string): Promise<ServiceResult<ExecutionInfo>> {
    return this.executeOperation('executeFunction', async () => {
      this.validateRequired(params, ['functionId']);

      const services = sessionId ? createSessionServices(sessionId) : { functions: adminFunctions };

      const execution = await services.functions.createExecution(
        params.functionId,
        params.body,
        params.async,
        params.xpath,
        params.method,
        params.headers
      );

      logger.info(`Function executed: ${params.functionId}, execution: ${execution.$id}`);
      return execution as ExecutionInfo;
    });
  }

  // Get execution
  async getExecution(functionId: string, executionId: string): Promise<ServiceResult<ExecutionInfo>> {
    return this.executeOperation('getExecution', async () => {
      this.validateRequired({ functionId, executionId }, ['functionId', 'executionId']);

      const execution = await adminFunctions.getExecution(functionId, executionId);
      return execution as ExecutionInfo;
    });
  }

  // List executions
  async listExecutions(
    functionId: string,
    pagination?: PaginationParams
  ): Promise<ServiceResult<PaginatedResult<ExecutionInfo>>> {
    return this.executeOperation('listExecutions', async () => {
      this.validateRequired({ functionId }, ['functionId']);

      const validatedPagination = this.validatePagination(pagination);

      const result = await adminFunctions.listExecutions(
        functionId,
        undefined, // queries
        validatedPagination.limit,
        validatedPagination.offset
      );

      const paginatedResult = this.formatPaginatedResult(
        result.executions as ExecutionInfo[],
        result.total,
        validatedPagination
      );

      return paginatedResult;
    });
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await adminFunctions.list();
      return true;
    } catch (error) {
      logger.error('Functions service health check failed', error);
      return false;
    }
  }
}
